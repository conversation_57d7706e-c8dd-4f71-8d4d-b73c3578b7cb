#ifndef VOLTAGECALIBRATIONWORKER_H
#define VOLTAGECALIBRATIONWORKER_H

#include <QObject>
#include <QThread>
#include <QTimer>
#include <QMessageBox>
#include <QApplication>
#include <QSerialPort>
#include <QTcpSocket>
#include <functional>
#include "WorkerConfig.h"

class VoltageCalibrationWorker : public QObject
{
    Q_OBJECT

public:
    explicit VoltageCalibrationWorker(QObject *parent = nullptr);
    ~VoltageCalibrationWorker();

    void setDeviceConfig(const CalDeviceConfig &config);

    // 命令处理函数类型定义
    using CommandHandlerFunc = std::function<QPair<bool, QString>(const QByteArray &, const QString &)>;
    void setCommandHandler(CommandHandlerFunc handler);

public slots:
    void startVoltageCalibration();
    void abortCalibration();

signals:
    void calibrationProgress(int progress);
    void calibrationFinished(bool success);
    void logMessage(const QString &message);
    void voltageDataUpdate(double voltage, double deviation, bool passed);

private slots:
    void performCalibration();

private:
    // 核心标定函数
    bool calibrateVoltageLevel(double targetVoltage, const QString &levelName);
    bool performVoltageLevelCalibration(double targetVoltage, int maxAttempts = 3);

    // 数据读取和处理
    double readVoltage();
    bool isCalibrationPassed(const QVector<double> &deviations, double threshold = 0.1);

    // Modbus命令生成
    QByteArray createVoltageWriteFrame(double voltage);
    QByteArray createVoltageReadFrame();

    // 通信函数
    QPair<bool, QString> sendCommand(const QByteArray &command, const QString &commandType);
    QByteArray createModbusCommand(const QString &hexCommand);
    uint16_t calculateCRC16(const QByteArray &data);
    bool interruptibleSleep(int seconds);
    bool interruptibleMSleep(int milliseconds);

    // 用户交互
    bool showUserPrompt(const QString &message);

private:
    CalDeviceConfig m_deviceConfig;
    CommandHandlerFunc m_commandHandler;
    bool m_abortRequested;
    bool m_isRunning;

    // 标定参数
    static const uint16_t VOLTAGE_WRITE_ADDR = 0x0441; // 电压写入地址
    static const uint16_t VOLTAGE_READ_ADDR = 0x003D;  // CH1电压读取地址
    static const int STABILIZE_DELAY = 2000;           // 稳定等待时间(ms)
    static const int READ_INTERVAL = 1000;             // 读取间隔(ms)
    static const int READ_COUNT = 10;                  // 读取次数
    static const double VOLTAGE_THRESHOLD = 0.1;       // 电压偏差阈值(mV)

    // 标定档位
    static const double VOLTAGE_LEVEL_0MV = 0.0;
    static const double VOLTAGE_LEVEL_50MV = 50.0;
};

#endif // VOLTAGECALIBRATIONWORKER_H
