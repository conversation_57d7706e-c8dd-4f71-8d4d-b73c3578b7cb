#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include "BatchCalibration.h"
#include "BatchVerification.h"
#include "DataProcessor.h"
#include "SerialHandler.h"
#include "NetworkHandler.h"
#include "VerificationDialog.h"
#include "VerificationWorker.h"
#include "CalibrationDialog.h"
#include "CalibrationWorker.h"
#include "VoltageCalibrationWorker.h"
#include "DbHandler.h"
#include "ExportTask.h"
#include <QStackedWidget>
#include <QSerialPort>     //Qt串口功能接口类
#include <QSerialPortInfo> //提供设备现有串行端口的信息
#include <QThread>
#include <QTimer>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QUrl>
#include <QUrlQuery>
#include <QJsonDocument>
#include <QJsonObject>
#include <QApplication>
#include <QProcess>
#include <QTemporaryFile>
#include <QAxObject>
#include <QAxWidget>
#include <QFileDialog>
#include <QMessageBox>
#include <Objbase.h>
#include <QProgressDialog>
#include <QtMath>
#include <numeric>
#include "ProjectDetailsDialog.h"
#include <QStandardItemModel>

QT_BEGIN_NAMESPACE
namespace Ui
{
    class MainWindow;
}
QT_END_NAMESPACE

const QMap<quint16, QString> baudRateMap = {
    {0x0000, "1200"},
    {0x0001, "2400"},
    {0x0002, "4800"},
    {0x0003, "9600"},
    {0x0004, "19200"},
    {0x0005, "38400"},
    {0x0006, "57600"},
    {0x0007, "115200"}};
const QMap<QString, QString> baudRateMap2 = {
    {"1200", "00 00"},
    {"2400", "01 00"},
    {"4800", "02 00"},
    {"9600", "03 00"},
    {"19200", "04 00"},
    {"38400", "05 00"},
    {"57600", "06 00"},
    {"115200", "07 00"}};

// 创建设备型号到配置的映射
struct DeviceFirstAddress
{
    uint16_t write_addr;
    uint16_t read_addr;
};

// 创建处理器实例（每种设备对应一个处理器）
class calDeviceProcessor : public DataProcessor
{
public:
    explicit calDeviceProcessor(QObject *parent = nullptr) : DataProcessor(parent) {}

    bool supportsCommand(const QString &command) const override
    {
        return command.startsWith("Cal_"); // 支持所有以该前缀开头的命令
    }

    void processData(const QByteArray &data, const QString &m_currentCommand) override
    {
        QString command = m_currentCommand;
        if (command == "Cal_ZCLOG334NTC_EndCal")
        {
            if (data.size() >= 5)
            {
                QString hexData = data.toHex(' ');
                QStringList hexList = hexData.split(" ");
                QString data1 = hexList.value(4, "").toUpper();
                emit resultReady(true, data1 == "4C" ? "success" : "fail", command);
            }
            else
            {
                emit resultReady(false, "fail", command);
            }
        }
        else if (command == "Cal_ReadBoardCardModel")
        {
            if (data.size() == 7)
            {
                quint16 value = static_cast<quint8>(data[3]) | (static_cast<quint8>(data[4]) << 8);

                // 获取类型和精度
                quint8 precision = static_cast<quint8>(value & 0xFF);
                quint8 type = static_cast<quint8>((value >> 8) & 0xFF);

                // 确定类型和精度
                QString cardType = (type == 0x01) ? "NTC" : (type == 0x02) ? "RTD"
                                                        : (type == 0x03)   ? "TC"
                                                                           : "未知类型";
                QString accuracy = (precision == 0x01) ? "01" : (precision == 0x02) ? "02"
                                                            : (precision == 0x03)   ? "03"
                                                                                    : "未知精度";

                // 返回格式为 "类型-精度"
                emit resultReady(true, cardType + "-" + accuracy, "Cal_ReadBoardCardModel");
            }
            else
            {
                emit resultReady(false, "error", "Cal_ReadBoardCardModel");
            }
        }
        else if (command == "Cal_ReadBoardCardNumber")
        {
            if (data.size() == 15)
            {
                // 数据已完全接收，进行处理
                QByteArray relevantData = data.mid(3, 10); // 提取从第4个字节开始的10个字节
                qDebug() << relevantData.toHex();

                QString asciiString = QString::fromLatin1(relevantData);
                qDebug() << "ASCII string: " << asciiString;

                emit resultReady(true, asciiString, "Cal_ReadBoardCardNumber");
            }
            else
            {
                emit resultReady(false, "error", "Cal_ReadBoardCardNumber");
            }
        }
        else if (command == "Cal_ReadNum")
        {
            if (data.size() == 15)
            {
                // 数据已完全接收，进行处理
                QByteArray relevantData = data.mid(3, 10); // 提取从第4个字节开始的10个字节
                qDebug() << relevantData.toHex();

                QString asciiString = QString::fromLatin1(relevantData);
                qDebug() << "ASCII string: " << asciiString;

                emit resultReady(true, asciiString, "Cal_ReadNum");
            }
            else
            {
                emit resultReady(false, "error", "Cal_ReadNum");
            }
        }
        else if (command == "Cal_ReadFilterNum")
        {
            if (data.size() == 7)
            {
                // 标准设备：小端格式（索引3为低字节，索引4为高字节）
                quint16 value = static_cast<quint8>(data[4]) << 8 | static_cast<quint8>(data[3]);
                qDebug() << "FilterNum:" << value;
                emit resultReady(true, QString::number(value), "Cal_ReadFilterNum");
            }
            else
            {
                emit resultReady(false, "error", "Cal_ReadFilterNum");
            }
        }
        else if (command == "Cal_ReadFilterNum_TM24ND")
        {
            if (data.size() == 7)
            {
                // TM24ND-P-S：大端格式（索引3为高字节，索引4为低字节）
                quint16 value = static_cast<quint8>(data[3]) << 8 | static_cast<quint8>(data[4]);
                qDebug() << "TM24ND FilterNum:" << value;
                emit resultReady(true, QString::number(value), "Cal_ReadFilterNum_TM24ND");
            }
            else
            {
                emit resultReady(false, "error", "Cal_ReadFilterNum_TM24ND");
            }
        }
        else if (command == "Cal_ReadFilterNum1618A")
        {
            if (data.size() == 7)
            {
                // 直接提取小端格式的2字节数据（索引3为低字节，索引4为高字节）
                quint16 value = static_cast<quint8>(data[4]) << 8 | static_cast<quint8>(data[3]);
                qDebug() << "1618A FilterNum:" << value;
                emit resultReady(true, QString::number(value), "Cal_ReadFilterNum1618A");
            }
            else
            {
                emit resultReady(false, "error", "Cal_ReadFilterNum1618A");
            }
        }
        else if (command == "Cal_ReadBaudRate")
        {
            if (data.size() == 7)
            {
                // 标准设备：小端格式
                quint16 value = static_cast<quint8>(data[3]) | (static_cast<quint8>(data[4]) << 8);

                QString baudRate = baudRateMap.value(value, "未知波特率");
                // qDebug() << "BaudRate:" << baudRate;
                emit resultReady(true, baudRate, "Cal_ReadBaudRate");
            }
            else
            {
                emit resultReady(false, "error", "Cal_ReadBaudRate");
            }
        }
        else if (command == "Cal_ReadBaudRate_TM24ND")
        {
            if (data.size() == 7)
            {
                // TM24ND-P-S：大端格式（索引3为高字节，索引4为低字节）
                quint16 value = static_cast<quint8>(data[3]) << 8 | static_cast<quint8>(data[4]);

                QString baudRate = baudRateMap.value(value, "未知波特率");
                qDebug() << "TM24ND BaudRate:" << baudRate;
                emit resultReady(true, baudRate, "Cal_ReadBaudRate_TM24ND");
            }
            else
            {
                emit resultReady(false, "error", "Cal_ReadBaudRate_TM24ND");
            }
        }
        else if (command == "Cal_WriteNum")
        {
            if (verifyCRC(data))
            {
                if (data.size() >= 15)
                {
                    QString hexData = data.toHex(' ');
                    QStringList hexList = hexData.split(" ");

                    // bool isSuccess = (hexList.at(2).toUpper() == "0A");
                    bool isSuccess = true;
                    emit resultReady(isSuccess, isSuccess ? "success" : "error", "Cal_WriteNum");
                }
                else
                {
                    emit resultReady(false, "error", "Cal_WriteNum");
                }
            }
            else
            {
                // CRC校验失败
                emit resultReady(false, "CRC verification failed", "Cal_WriteNum");
            }
        }
        else if (command == "Cal_WriteNum_TM24ND")
        {
            // TM24ND-P-S的0E功能码响应：只返回确认信息，不返回写入的数据
            if (verifyCRC(data))
            {
                if (data.size() == 8)
                {
                    QString hexData = data.toHex(' ');
                    QStringList hexList = hexData.split(" ");

                    // 检查功能码是否为0E
                    bool isSuccess = (hexList.at(1).toUpper() == "0E");
                    emit resultReady(isSuccess, isSuccess ? "success" : "error", "Cal_WriteNum_TM24ND");
                }
                else
                {
                    emit resultReady(false, "error", "Cal_WriteNum_TM24ND");
                }
            }
            else
            {
                // CRC校验失败
                emit resultReady(false, "CRC verification failed", "Cal_WriteNum_TM24ND");
            }
        }
        else if (command == "Cal_WriteBoardCardNumber")
        {
            if (verifyCRC(data))
            {
                if (data.size() >= 15)
                {
                    QString hexData = data.toHex(' ');
                    QStringList hexList = hexData.split(" ");

                    // bool isSuccess = (hexList.at(2).toUpper() == "0A");
                    bool isSuccess = true;
                    emit resultReady(isSuccess, isSuccess ? "success" : "error", "Cal_WriteBoardCardNumber");
                }
                else
                {
                    emit resultReady(false, "error", "Cal_WriteBoardCardNumber");
                }
            }
            else
            {
                // CRC校验失败
                emit resultReady(false, "CRC verification failed", "Cal_WriteBoardCardNumber");
            }
        }
        else if (command == "Cal_WriteBaudRate")
        {
            if (data.size() == 8)
            {
                // 标准设备：小端格式
                quint16 value = static_cast<quint8>(data[4]) | (static_cast<quint8>(data[5]) << 8);

                QString baudRate = baudRateMap.value(value, "未知波特率");

                QString hexData = data.toHex(' ');
                QStringList hexList = hexData.split(" ");

                bool isSuccess = (hexList.at(1).toUpper() == "06");
                emit resultReady(isSuccess, isSuccess ? baudRate : "error", "Cal_WriteBaudRate");
            }
            else
            {
                emit resultReady(false, "error", "Cal_WriteBaudRate");
            }
        }
        else if (command == "Cal_WriteBaudRate_TM24ND")
        {
            if (data.size() == 8)
            {
                // TM24ND-P-S：大端格式（索引4为高字节，索引5为低字节）
                quint16 value = static_cast<quint8>(data[4]) << 8 | static_cast<quint8>(data[5]);

                QString baudRate = baudRateMap.value(value, "未知波特率");

                QString hexData = data.toHex(' ');
                QStringList hexList = hexData.split(" ");

                bool isSuccess = (hexList.at(1).toUpper() == "06");
                emit resultReady(isSuccess, isSuccess ? baudRate : "error", "Cal_WriteBaudRate_TM24ND");
            }
            else
            {
                emit resultReady(false, "error", "Cal_WriteBaudRate_TM24ND");
            }
        }
        else if (command == "Cal_WriteInit" || command == "Cal_WriteChannelRest" ||
                 command == "Cal_WriteInit_TM24ND" || command == "Cal_WriteChannelRest_TM24ND")
        {
            if (data.size() == 8)
            {
                QString hexData = data.toHex(' ');
                QStringList hexList = hexData.split(" ");

                bool isSuccess = (hexList.at(1).toUpper() == "06");
                emit resultReady(isSuccess, isSuccess ? "success" : "error", command);
            }
            else
            {
                emit resultReady(false, "error", command);
            }
        }
        else if (command == "Cal_WriteFilterNum")
        {
            if (data.size() == 8)
            {
                QString hexData = data.toHex(' ');
                QStringList hexList = hexData.split(" ");

                bool isSuccess = (hexList.at(1).toUpper() == "06");
                emit resultReady(isSuccess, isSuccess ? "success" : "error", "Cal_WriteFilterNum");
            }
            else
            {
                emit resultReady(false, "error", "Cal_WriteFilterNum");
            }
        }
        else if (command == "Cal_WriteFilterNum_TM24ND")
        {
            if (data.size() == 8)
            {
                QString hexData = data.toHex(' ');
                QStringList hexList = hexData.split(" ");

                bool isSuccess = (hexList.at(1).toUpper() == "06");
                emit resultReady(isSuccess, isSuccess ? "success" : "error", "Cal_WriteFilterNum_TM24ND");
            }
            else
            {
                emit resultReady(false, "error", "Cal_WriteFilterNum_TM24ND");
            }
        }
        else if (command == "Cal_WriteFilterNum1618A")
        {
            if (data.size() == 8)
            {
                QString hexData = data.toHex(' ');
                QStringList hexList = hexData.split(" ");

                bool isSuccess = (hexList.at(1).toUpper() == "06");
                emit resultReady(isSuccess, isSuccess ? "success" : "error", "Cal_WriteFilterNum1618A");
            }
            else
            {
                emit resultReady(false, "error", "Cal_WriteFilterNum1618A");
            }
        }
        else if (command == "Cal_EnterTransparentMode")
        {
            const int expectedLength = 14;
            if (data.size() >= expectedLength)
            {
                // 将 receivedData 转换为 QString 类型方便处理
                QString receivedString = QString::fromLatin1(data);
                QString resultStr;
                if (receivedString.contains("start;cal;ok"))
                {
                    resultStr = "唤醒成功";
                }
                else
                {
                    resultStr = "唤醒失败";
                }
                emit resultReady(true, resultStr, "Cal_EnterTransparentMode");
            }
            else
            {
                emit resultReady(false, "数据长度不匹配", "Cal_EnterTransparentMode");
            }
        }
        else if (command == "Cal_QuitTransparentMode")
        {
            const int expectedLength = 12;
            if (data.size() >= expectedLength)
            {
                // 将 receivedData 转换为 QString 类型方便处理
                QString receivedString = QString::fromLatin1(data);
                QString resultStr;
                if (receivedString.contains("end;cal;ok"))
                {
                    resultStr = "关闭成功";
                }
                else
                {
                    resultStr = "关闭失败";
                }
                emit resultReady(true, resultStr, "Cal_QuitTransparentMode");
            }
            else
            {
                emit resultReady(false, "数据长度不匹配", "Cal_QuitTransparentMode");
            }
        }
        else if (command == "Cal_ReadNum_String")
        {
            const int expectedLength = 12;
            if (data.size() >= expectedLength)
            {
                // 将 receivedData 转换为 QString 类型方便处理
                QString receivedString = QString::fromLatin1(data);
                QString trimmedStr = receivedString.trimmed();

                // 匹配任意10个字符
                QRegExp rx(".{10}");
                if (rx.exactMatch(trimmedStr))
                {
                    emit resultReady(true, trimmedStr, "Cal_ReadNum_String");
                }
                else
                {
                    emit resultReady(false, "序列号格式错误", "Cal_ReadNum_String");
                }
            }
            else
            {
                emit resultReady(false, "数据长度不匹配", "Cal_ReadNum_String");
            }
        }
        else if (command == "Cal_WriteNum_String")
        {
            const int expectedLength = 8;
            if (data.size() >= expectedLength)
            {
                // 将 receivedData 转换为 QString 类型方便处理
                QString receivedString = QString::fromLatin1(data);
                QString resultStr;
                if (receivedString.contains("SET OK"))
                {
                    resultStr = "写入序列号成功";
                }
                else
                {
                    resultStr = "写入序列号失败";
                }
                emit resultReady(true, resultStr, "Cal_WriteNum_String");
            }
            else
            {
                emit resultReady(false, "数据长度不匹配", "Cal_WriteNum_String");
            }
        }
        else if (command == "Cal_WriteCalParams")
        {
            if (verifyCRC(data))
            {
                if (data.size() >= 9)
                { // 同时支持单精度（9）与双精度写入（13）
                    QString hexData = data.toHex(' ');
                    QStringList hexList = hexData.split(" ");

                    bool isSuccess = (hexList.at(1).toUpper() == "10");
                    emit resultReady(isSuccess, isSuccess ? "success" : "error", "Cal_WriteCalParams");
                }
                else
                {
                    emit resultReady(false, "error", "Cal_WriteCalParams");
                }
            }
            else
            {
                // CRC校验失败
                emit resultReady(false, "CRC verification failed", "Cal_WriteCalParams");
            }
        }
        else if (command == "Cal_WriteCalParams_TM24ND")
        {
            // TM24ND-P-S的10功能码响应：只返回确认信息，不返回写入的数据
            if (verifyCRC(data))
            {
                if (data.size() == 8)
                {
                    QString hexData = data.toHex(' ');
                    QStringList hexList = hexData.split(" ");

                    // 检查功能码是否为10
                    bool isSuccess = (hexList.at(1).toUpper() == "10");
                    emit resultReady(isSuccess, isSuccess ? "success" : "error", "Cal_WriteCalParams_TM24ND");
                }
                else
                {
                    emit resultReady(false, "error", "Cal_WriteCalParams_TM24ND");
                }
            }
            else
            {
                // CRC校验失败
                emit resultReady(false, "CRC verification failed", "Cal_WriteCalParams_TM24ND");
            }
        }
        else if (command == "Cal_ReadARoundRVals")
        {
            if (verifyCRC(data))
            {
                QString formatted_double;
                if (data.size() == 13)
                { // 确保有足够的数据（3个起始字节 + 8个数据字节 + 至少2个CRC字节）
                    // 将字节数据转换为十六进制字符串
                    QString hexData = data.toHex(' ');
                    QStringList hexList = hexData.split(" ");
                    QString combinedData;

                    // 按照小端序重排数据字节（反转顺序）
                    // 从索引3开始，收集8个数据字节
                    for (int i = 10; i >= 3; i--)
                    {
                        combinedData += hexList.at(i);
                    }

                    // 将十六进制字符串转换为64位整数
                    quint64 hex_long = combinedData.toULongLong(nullptr, 16);

                    // 通过内存重解释将64位整数转换为双精度浮点数
                    double double_data = *reinterpret_cast<double *>(&hex_long);

                    // 格式化浮点数，保留6位小数
                    formatted_double = QString::asprintf("%.6f", double_data);

                    qDebug() << "formattedDouble: " << formatted_double;
                }
                else if (data.size() == 9)
                { // 3个起始字节 + 4个数据字节 + 2个CRC字节  单精度
                    // 将字节数据转换为十六进制字符串
                    QString hexData = data.toHex(' ');
                    QStringList hexList = hexData.split(" ");
                    QString combinedData;

                    // 按照小端序重排数据字节（反转顺序）
                    // 从索引3开始，收集4个数据字节
                    for (int i = 6; i >= 3; i--)
                    {
                        combinedData += hexList.at(i);
                    }

                    // 将十六进制字符串转换为32位整数
                    quint32 hex_int = combinedData.toUInt(nullptr, 16);

                    // 通过内存重解释将32位整数转换为单精度浮点数
                    float float_data = *reinterpret_cast<float *>(&hex_int);

                    // 格式化浮点数，保留6位小数
                    formatted_double = QString::asprintf("%.6f", float_data);

                    qDebug() << "Single precision - formattedDouble: " << formatted_double;
                }
                // 将结果发送到相应的处理函数
                emit resultReady(true, formatted_double, command);
            }
            else
            {
                // CRC校验失败
                emit resultReady(false, "CRC verification failed", command);
            }
        }
    }

private:
    uint16_t calculateCRC16(const QByteArray &data) const
    {
        uint16_t crc = 0xFFFF;
        for (char ch : data)
        {
            crc ^= static_cast<uint8_t>(ch);
            for (int i = 0; i < 8; ++i)
            {
                if (crc & 0x0001)
                {
                    crc = (crc >> 1) ^ 0xA001;
                }
                else
                {
                    crc = crc >> 1;
                }
            }
        }
        return crc;
    }

    bool verifyCRC(const QByteArray &data) const
    {
        if (data.size() < 3)
        {
            return false; // 数据长度不足以包含 CRC
        }
        QByteArray dataWithoutCRC = data.left(data.size() - 2);
        uint16_t calculatedCRC = calculateCRC16(dataWithoutCRC);
        uint16_t receivedCRC = static_cast<uint8_t>(data.at(data.size() - 2)) |
                               (static_cast<uint8_t>(data.at(data.size() - 1)) << 8);
        return calculatedCRC == receivedCRC;
    }
};

class _1220DeviceProcessor : public DataProcessor
{
public:
    explicit _1220DeviceProcessor(QObject *parent = nullptr) : DataProcessor(parent) {}

    bool supportsCommand(const QString &command) const override
    {
        return command.startsWith("1220_"); // 支持所有以该前缀开头的命令
    }

    void processData(const QByteArray &data, const QString &command) override
    {
        if (command == "1220_ControlSwitch")
        {
            if (data.size() == 9)
            {
                QString hexData = data.toHex(' ');
                QStringList hexList = hexData.split(" ");

                bool isSuccess = (hexList.at(1).toUpper() == "10");
                emit resultReady(isSuccess, isSuccess ? "success" : "error", "1220_ControlSwitch");
            }
            else
            {
                emit resultReady(false, "error", "1220_ControlSwitch");
            }
        }
    }
};

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private:
    Ui::MainWindow *ui;

    QString m_globalHeadStr = "01";

    QStringList communicationTypes = {"串口", "网络"};
    QStringList baudRates = {"1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200"};
    QStringList referGears = {"0", "10Ω", "100Ω", "200Ω", "350Ω", "1kΩ", "2kΩ", "5kΩ", "10kΩ", "20kΩ"};

    // 透传状态标志
    bool isWakeUp = false;

    // 需要透传的设备型号列表
    QStringList transparentModeDevices = {"1611A-HT(PT100)", "1611A-HT(PT1000)", "1618A-L"};
    QString currentTransparentDevice = ""; // 当前透传模式设备型号;

    void initializeUI();
    void refreshSerial_Cal();
    void refreshSerial_1220();
    void updateChannelNums_Cal(const QString &model);
    void onDeviceModelChanged(int index); // 设备型号切换处理
    void setComboBoxValue(QComboBox *comboBox, const QStringList &items, const QString &value);
    void adjustStackedWidgetMargins(QStackedWidget *stackedWidget);
    void setReferenceRowVisible(int rowNumber, bool visible); // 设置参考电阻行的可见性

    bool checkConnection();
    QPair<bool, QString> sendCommand_Cal(const QByteArray &data, const QString &command);
    QPair<bool, QString> sendCommand_1220(const QByteArray &data, const QString &command);

    QByteArray createModbusCommand(const QString &hexCommand);
    uint16_t calculateCRC16(const QByteArray &data);

    // 辅助函数：根据设备类型格式化字节序
    QString formatValueForDevice(const QString &deviceModel, uint16_t value);

    void generateCalTable(CalDeviceConfig &device);
    void generateAdjTable(AdjDeviceConfig &device);

    calDeviceProcessor *m_calDeviceProcessor_Serial;
    calDeviceProcessor *m_calDeviceProcessor_Network;
    SerialHandlerPro *m_calDeviceSerialHandler;
    NetworkHandler *m_calDeviceNetworkHandler;

    _1220DeviceProcessor *m_1220DeviceProcessor_Serial;
    _1220DeviceProcessor *m_1220DeviceProcessor_Network;
    SerialHandlerPro *m_1220DeviceSerialHandler;
    NetworkHandler *m_1220DeviceNetworkHandler;

    CalibrationDialog *calibrationDialog{nullptr};
    VerificationDialog *verificationDialog{nullptr};

    DbHandler *dbHandler;

    QThread *calibrationThread;
    CalibrationWorker *calibrationWorker;

    QThread *verificationThread;
    VerificationWorker *verificationWorker;

    QThread *voltageCalibrationThread;
    VoltageCalibrationWorker *voltageCalibrationWorker;

    // 静态命令处理函数，用于传递给工作线程
    static QPair<bool, QString> calCommandHandler(const QByteArray &command, const QString &commandType);
    static QPair<bool, QString> deviceCommandHandler(const QByteArray &command, const QString &commandType);

    // 保存主窗口实例的静态指针，用于静态函数中访问
    static MainWindow *s_instance;

    void connectToDatabase();
    void loadCalibrationHistory();
    void loadCalibrationHistory_Adj();

    QString getUnitFromModel(const QString &deviceModel);

    QProgressDialog *progressDialog;

    ProjectDetailsDialog *projectDetailsDialog;

    void updateLayoutSpacing();

    BatchCalibrationManager *batchCalibrationManager;
    BatchVerificationManager *batchVerificationManager;
    bool isBatchCalibrationInProgress;
    bool isBatchVerificationInProgress;

    void startBatchCalibrationInThread(CalDeviceConfig &device);
    void startBatchVerificationInThread(AdjDeviceConfig &device);

private slots:
    void onCommunicationType_CalChanged(int index);
    void onCommunicationType_1220Changed(int index);

    void on_connectButton_Cal_clicked();
    void on_disconnectButton_Cal_clicked();
    void on_connectButton_1220_clicked();
    void on_disconnectButton_1220_clicked();

    void on_startCalibration_clicked();
    void on_startAdjustment_clicked();

    // 处理校准对话框的命令
    void on_signal_readCommand_CalDialog(const QString &deviceModel, const QString &featureCode);
    void on_signal_writeCommand_CalDialog(const QString &deviceModel, const QString &featureCode, const QString &paramVal);
    void on_signal_writeCommand_CalDialog_Reset(const QString &deviceModel, const QString &featureCode, const QString &paramVal, const QString &referRVal);
    void on_signal_startCal_CalDialog(const QString &deviceModel, const QString &featureCode, const int channelNums, const QString &referRVal);

    void on_signal_readCommand_AdjDialog(const QString &deviceModel, const QString &featureCode);
    void on_signal_startCal_AdjDialog(const QString &deviceModel, const QString &featureCode);

    void handleCalResults(const QPair<bool, QString> &result, const QString &errorMsg, const QString &successMsg);

    // 启动标定线程
    void startCalibrationInThread(CalDeviceConfig &device);

    // 处理校准工作线程的信号
    void handleCalibrationStarted();
    void handleCalibrationFinished(bool success);
    void handleCalibrationProgress(int channel, int progress);
    void handleLogMessage(const QString &message);
    void handleUpdateChannelData(int channel, double resistance, double deviation, bool passed);
    void handleSaveResultsRequested(const CalDeviceConfig &config, const QVector<QPair<double, double>> &results);
    void abortCalibration();

    void onHistoryItemDoubleClicked(QListWidgetItem *item);
    void onHistoryDelBtnClicked();

    void enterTransparentModeClicked();
    void quitTransparentModeClicked();
    void enterTransparentModeClicked_Adj();
    void quitTransparentModeClicked_Adj();
    void resetTransparentModeState(); // 重置透传模式状态

    bool checkTransparentModeRequirement(const QString &deviceModel, const QString &operationName); // 检查透传模式操作要求
    bool checkTransparentModeBeforeOperation(const QString &operationName);                         // 检查当前是否处于透传模式，如果是则提示退出

protected:
    void closeEvent(QCloseEvent *event) override; // 重写关闭事件

private:
    // 分批标定
    void handleBatchCalibrationCompleted(bool success);
    void handleBatchProgress(int channel, int progress);
    void handleReconnectionRequired(const QVector<int> &channelsToConnect);
    void handleWaitingForUserConfirmation(int batchIndex);
    void handleBatchStarting(int batchIndex, int totalBatches, const QVector<int> &channelsInBatch);

    void startVerificationInThread(AdjDeviceConfig &device);
    void handleVerificationStarted();
    void handleVerificationFinished(bool success);
    void handleVerificationProgress(int channel, int progress);
    void handleLogMessage_Adj(const QString &message);
    void handleUpdateChannelData_Adj(double referenceValue, int channel, QVector<double> resistances, double measuredResistance,
                                     double deviationFromReference, double allowedDeviation, double equivalentTempDeviation, bool calibrationResult);
    void handleSaveResultsRequested_Adj();
    void abortVerification();

    // 分批校准
    void handleBatchVerificationCompleted(bool success);
    void handleBatchProgress_Adj(int channel, int progress);
    void handleReconnectionRequired_Adj(const QVector<int> &channelsToConnect, int referenceIndex);
    void handleBatchStarting_Adj(int batchIndex, int totalBatches, int referenceIndex, const QVector<int> &channelsInBatch);

    int addProject(const ProjectData &projectData);
    int addReferenceValue(int projectId, const QString &referenceName, double referenceValue);
    bool addChannelData(int referenceId, const ChannelData &data);

    bool getProjectInfo(int projectId, QString &projectName, QString &deviceModel,
                        QString &serialNumber, QDateTime &calibrationDate);
    QVector<ReferenceData> getProjectReferenceValues(int projectId);
    QVector<ChannelData> getReferenceChannelData(int referenceId);
    ProjectData getProject(int projectId);

    void on_endCal_Adj_clicked();
    void on_exportBtn_Adj_clicked();
    void updateProgress(int value);
    void closeProgressDialog();
    void onReportExportFinished(bool success, const QString &filePath);
    void on_recalibrateButton_clicked();
    void on_endCal_CalDialog_clicked();
    void on_recalibrateButton_CalDialog_clicked();

    bool validateTableData();
    ProjectData extractProjectDataFromTable();
    bool saveProjectToDatabase(const ProjectData &projectData);

    void onHistoryItemDoubleClicked_Adj(QListWidgetItem *item);
    void loadProjectData(int projectId);
    QVector<double> convertJsonToMeasuredResistances(const QString &json);
    ProjectData getProjectData(int projectId);
    void fillTableWithProjectData(const ProjectData &projectData);

    bool deleteProject(int projectId);
    void onHistoryDelBtnClicked_Adj();
    void on_historyListWidget_Adj_customContextMenuRequested(const QPoint &pos);

    void showAutoFadeInformation(QWidget *parent, const QString &title, const QString &message);

signals:
    void signal_readCommand_CalDialog_Result(const QString &serialNumber, const QString &featureCode);
    void signal_writeCommand_CalDialog_Result(const QString &result, const QString &featureCode);
    void signal_startCal_CalDialog_Result(const QString &result, const QString &featureCode);

    void signal_readCommand_AdjDialog_Result(const QString &serialNumber, const QString &featureCode);

    // 信号用于通知校准工作线程开始工作
    void startCalibration1();
    void startCalibration2();
    void restartCalibration1(int channel);
    void restartCalibration2(int groupIndex, double referenceValue, int channel);

    // 返回更新Adj UI表格的结果
    void uiUpdateFinished();
};
#endif // MAINWINDOW_H
