﻿#ifndef VERIFICATIONDIALOG_H
#define VERIFICATIONDIALOG_H
#include "ui_VerificationDialog.h"
#include <QDialog>
#include <QTimer>

namespace Ui
{
    class VerificationDialog;
}

class VerificationDialog : public QDialog
{
    Q_OBJECT

public:
    explicit VerificationDialog(QWidget *parent = nullptr);
    ~VerificationDialog();

    void initVerificationDialog(const QString &deviceModel); // 传入设备型号
    Ui::VerificationDialog *getUi() { return ui; }

public slots:
    void on_signal_readCommand_AdjDialog_Result(const QString &serialNumber, const QString &featureCode);
    // void on_signal_writeCommand_AdjDialog_Result(const QString &result, const QString &featureCode);

private slots:
    void closeEvent(QCloseEvent *event);
    void on_readBtn_AdjDialog_clicked();
    void on_readBtn_Board_AdjDialog_clicked();
    void on_readBtn_Board_AdjDialog_2_clicked();
    void on_startCal_Adj_clicked();

    // 电压标定相关槽函数
    void on_startVoltageCalibration_Btn_clicked();
    void on_abortVoltageCalibration_Btn_clicked();
    void onVoltageCalibrationProgress(int progress);
    void onVoltageCalibrationFinished(bool success);
    void onVoltageCalibrationLogMessage(const QString &message);
    void onVoltageCalibrationDataUpdate(double voltage, double deviation, bool passed);

private:
    Ui::VerificationDialog *ui;
    void loadSettings();
    QTimer *resizeTimer;

    // 电压标定相关成员变量
    bool isVoltageCalibrationRunning;
    int currentVoltageLevel; // 当前电压档位 (0=0mV, 1=50mV)
    int currentRound;        // 当前轮次
    int currentAttempt;      // 当前尝试次数
    void initVoltageCalibrationTable();
    void updateVoltageCalibrationUI(bool isTC);

protected:
    void resizeEvent(QResizeEvent *event) override;
    void showEvent(QShowEvent *event) override;

signals:
    void signal_readCommand_AdjDialog(const QString &deviceModel, const QString &featureCode);
    // void signal_writeCommand_AdjDialog(const QString &deviceModel, const QString &featureCode, const QString &paramVal);
    void signal_startCal_AdjDialog(const QString &deviceModel, const QString &featureCode);

    // 电压标定相关信号
    void signal_startVoltageCalibration(const QString &deviceModel);
    void signal_abortVoltageCalibration();
};

#endif // VERIFICATIONDIALOG_H
