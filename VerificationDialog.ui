<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>VerificationDialog</class>
 <widget class="QDialog" name="VerificationDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1006</width>
    <height>705</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" columnstretch="3,4,3">
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox_5">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="title">
      <string>校准参数</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <layout class="QGridLayout" name="gridLayout_2" columnstretch="4,5,5">
        <item row="0" column="2">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="1" column="2">
         <widget class="QPushButton" name="readBtn_AdjDialog">
          <property name="text">
           <string>读序列号</string>
          </property>
         </widget>
        </item>
        <item row="11" column="2">
         <widget class="QPushButton" name="readBtn_Board_AdjDialog_2">
          <property name="text">
           <string>读板卡精度</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="deviceModel_Adj">
          <property name="minimumSize">
           <size>
            <width>86</width>
            <height>0</height>
           </size>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="6" column="0">
         <widget class="QLabel" name="label_21">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>气    压：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="5" column="0">
         <widget class="QLabel" name="label_17">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>环境湿度：</string>
          </property>
         </widget>
        </item>
        <item row="7" column="2">
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="5" column="2">
         <widget class="QLabel" name="label_18">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>%RH</string>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QLineEdit" name="ambientTemp">
          <property name="text">
           <string>25</string>
          </property>
         </widget>
        </item>
        <item row="7" column="0">
         <widget class="QLabel" name="label_22">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>校准员：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="12" column="0">
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>板卡型号：</string>
          </property>
         </widget>
        </item>
        <item row="12" column="1" colspan="2">
         <widget class="QComboBox" name="boardCardModel_Combo_Adj"/>
        </item>
        <item row="8" column="1" colspan="2">
         <widget class="QLineEdit" name="reportNum"/>
        </item>
        <item row="5" column="1">
         <widget class="QLineEdit" name="ambientHum">
          <property name="text">
           <string>40</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_11">
          <property name="text">
           <string>序列号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="11" column="1">
         <widget class="QLineEdit" name="serialNumEdit_Board_Adj_2">
          <property name="minimumSize">
           <size>
            <width>86</width>
            <height>0</height>
           </size>
          </property>
          <property name="readOnly">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item row="4" column="2">
         <widget class="QLabel" name="label_16">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>°C</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_10">
          <property name="text">
           <string>设备型号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="8" column="0">
         <widget class="QLabel" name="label_20">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>报告编号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="6" column="1">
         <widget class="QLineEdit" name="ambientPre">
          <property name="text">
           <string>101</string>
          </property>
         </widget>
        </item>
        <item row="3" column="0" colspan="3">
         <widget class="Line" name="line">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="10" column="2">
         <widget class="QPushButton" name="readBtn_Board_AdjDialog">
          <property name="text">
           <string>读板卡编号</string>
          </property>
         </widget>
        </item>
        <item row="10" column="1">
         <widget class="QLineEdit" name="serialNumEdit_Board_Adj">
          <property name="minimumSize">
           <size>
            <width>86</width>
            <height>0</height>
           </size>
          </property>
          <property name="readOnly">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item row="7" column="1">
         <widget class="QLineEdit" name="calibrator">
          <property name="text">
           <string>张大晨</string>
          </property>
         </widget>
        </item>
        <item row="6" column="2">
         <widget class="QLabel" name="label_19">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>kpa</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QLabel" name="label_23">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>环境温度：</string>
          </property>
         </widget>
        </item>
        <item row="11" column="0">
         <widget class="QLabel" name="label_3">
          <property name="text">
           <string>板卡精度：</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLineEdit" name="serialNumEdit_Adj">
          <property name="minimumSize">
           <size>
            <width>86</width>
            <height>0</height>
           </size>
          </property>
          <property name="readOnly">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item row="9" column="0" colspan="3">
         <widget class="Line" name="line_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="10" column="0">
         <widget class="QLabel" name="label">
          <property name="text">
           <string>板卡编号：</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QPushButton" name="enterTransparentMode_Adj">
          <property name="text">
           <string>进入透传</string>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QPushButton" name="quitTransparentMode_Adj">
          <property name="text">
           <string>退出透传</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="transparentModeLabel_Adj">
          <property name="text">
           <string>透传模式：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QGroupBox" name="groupBox_3">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <layout class="QGridLayout" name="gridLayout_7">
      <item row="0" column="0">
       <widget class="QLabel" name="label_13">
        <property name="text">
         <string>校准日志信息</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="2">
       <widget class="QTextEdit" name="textEdit_Adj">
        <property name="minimumSize">
         <size>
          <width>300</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_7">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>179</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QGroupBox" name="groupBox_4">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <layout class="QGridLayout" name="gridLayout_8">
      <item row="0" column="2">
       <widget class="QPushButton" name="historyDelBtn_Adj">
        <property name="text">
         <string>删除</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QPushButton" name="exportBtn_Adj">
        <property name="text">
         <string>导出报告</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="4">
       <widget class="QListWidget" name="historyListWidget_Adj">
        <property name="minimumSize">
         <size>
          <width>300</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_9">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>212</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_14">
        <property name="text">
         <string>校准历史记录</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0" colspan="3">
    <widget class="QGroupBox" name="groupBox_6">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <layout class="QGridLayout" name="gridLayout_14" columnstretch="2,3,4,3,4,3,4">
        <item row="3" column="4">
         <widget class="QLineEdit" name="refResistorSerialNum4">
          <property name="minimumSize">
           <size>
            <width>90</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>150</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="2" column="5">
         <widget class="QLabel" name="label_54">
          <property name="text">
           <string>有效期至：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLabel" name="label_15">
          <property name="text">
           <string>型号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="4">
         <widget class="QLineEdit" name="tempBridgeSerialNum">
          <property name="minimumSize">
           <size>
            <width>95</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>150</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="0" column="5">
         <widget class="QLabel" name="label_30">
          <property name="text">
           <string>有效期至：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="3" column="6">
         <widget class="QDateTimeEdit" name="refResistorDateTimeEdit4">
          <property name="dateTime">
           <datetime>
            <hour>0</hour>
            <minute>0</minute>
            <second>0</second>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </datetime>
          </property>
          <property name="date">
           <date>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </date>
          </property>
          <property name="displayFormat">
           <string>yyyy/MM/dd</string>
          </property>
         </widget>
        </item>
        <item row="1" column="3">
         <widget class="QLabel" name="label_27">
          <property name="text">
           <string>序列号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QComboBox" name="refResistorModel"/>
        </item>
        <item row="1" column="6">
         <widget class="QDateTimeEdit" name="refResistorDateTimeEdit">
          <property name="dateTime">
           <datetime>
            <hour>0</hour>
            <minute>0</minute>
            <second>0</second>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </datetime>
          </property>
          <property name="date">
           <date>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </date>
          </property>
          <property name="displayFormat">
           <string>yyyy/MM/dd</string>
          </property>
         </widget>
        </item>
        <item row="3" column="2">
         <widget class="QComboBox" name="refResistorModel4"/>
        </item>
        <item row="0" column="0">
         <widget class="QCheckBox" name="tempBridgeCheck">
          <property name="text">
           <string>参考电阻 1</string>
          </property>
         </widget>
        </item>
        <item row="1" column="4">
         <widget class="QLineEdit" name="refResistorSerialNum">
          <property name="minimumSize">
           <size>
            <width>90</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>150</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QCheckBox" name="refResistorCheck4">
          <property name="text">
           <string>参考电阻 4</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="tempBridgeLabel">
          <property name="text">
           <string>型号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="2" column="3">
         <widget class="QLabel" name="label_28">
          <property name="text">
           <string>序列号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLabel" name="label_24">
          <property name="text">
           <string>型号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QComboBox" name="tempBridgeModel"/>
        </item>
        <item row="2" column="2">
         <widget class="QComboBox" name="voltSourceModel"/>
        </item>
        <item row="3" column="5">
         <widget class="QLabel" name="label_55">
          <property name="text">
           <string>有效期至：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QCheckBox" name="refResistorCheck">
          <property name="text">
           <string>参考电阻 2</string>
          </property>
         </widget>
        </item>
        <item row="0" column="6">
         <widget class="QDateTimeEdit" name="tempBridgeDateTimeEdit">
          <property name="dateTime">
           <datetime>
            <hour>0</hour>
            <minute>0</minute>
            <second>0</second>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </datetime>
          </property>
          <property name="date">
           <date>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </date>
          </property>
          <property name="displayFormat">
           <string>yyyy/MM/dd</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QCheckBox" name="voltSourceCheck">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="text">
           <string>参考电阻 3</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QLabel" name="label_25">
          <property name="text">
           <string>序列号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="5">
         <widget class="QLabel" name="label_53">
          <property name="text">
           <string>有效期至：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="2" column="4">
         <widget class="QLineEdit" name="voltSourceSerialNum">
          <property name="minimumSize">
           <size>
            <width>90</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>150</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLabel" name="label_26">
          <property name="text">
           <string>型号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="2" column="6">
         <widget class="QDateTimeEdit" name="voltSourceDateTimeEdit">
          <property name="dateTime">
           <datetime>
            <hour>0</hour>
            <minute>0</minute>
            <second>0</second>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </datetime>
          </property>
          <property name="date">
           <date>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </date>
          </property>
          <property name="displayFormat">
           <string>yyyy/MM/dd</string>
          </property>
         </widget>
        </item>
        <item row="3" column="3">
         <widget class="QLabel" name="label_29">
          <property name="text">
           <string>序列号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QCheckBox" name="refResistorCheck5">
          <property name="text">
           <string>参考电阻 5</string>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QLabel" name="label_31">
          <property name="text">
           <string>型号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="4" column="2">
         <widget class="QComboBox" name="refResistorModel5"/>
        </item>
        <item row="4" column="3">
         <widget class="QLabel" name="label_32">
          <property name="text">
           <string>序列号：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="4" column="4">
         <widget class="QLineEdit" name="refResistorSerialNum5">
          <property name="minimumSize">
           <size>
            <width>90</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>150</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="4" column="5">
         <widget class="QLabel" name="label_56">
          <property name="text">
           <string>有效期至：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="4" column="6">
         <widget class="QDateTimeEdit" name="refResistorDateTimeEdit5">
          <property name="dateTime">
           <datetime>
            <hour>0</hour>
            <minute>0</minute>
            <second>0</second>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </datetime>
          </property>
          <property name="date">
           <date>
            <year>2024</year>
            <month>8</month>
            <day>13</day>
           </date>
          </property>
          <property name="displayFormat">
           <string>yyyy/MM/dd</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="0" colspan="3">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <layout class="QGridLayout" name="gridLayout_5">
      <item row="0" column="5">
       <widget class="QPushButton" name="restartCal_Adj">
        <property name="text">
         <string>复校</string>
        </property>
       </widget>
      </item>
      <item row="0" column="4">
       <widget class="QPushButton" name="abortCal_Adj">
        <property name="text">
         <string>停止校准</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QPushButton" name="startCal_Adj">
        <property name="text">
         <string>开始校准</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_8">
        <property name="text">
         <string>电阻校准数据</string>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QProgressBar" name="progressBar_Adj">
        <property name="value">
         <number>0</number>
        </property>
       </widget>
      </item>
      <item row="0" column="6">
       <widget class="QPushButton" name="endCal_Adj">
        <property name="text">
         <string>保存</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="7">
       <widget class="QTableWidget" name="readDataTable_Adj"/>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="0" colspan="3">
    <widget class="QGroupBox" name="groupBox_VoltageCalibration">
     <property name="title">
      <string>电压标定功能</string>
     </property>
     <property name="visible">
      <bool>false</bool>
     </property>
     <layout class="QGridLayout" name="gridLayout_VoltageCalibration">
      <item row="0" column="0">
       <widget class="QLabel" name="label_VoltageCalibration">
        <property name="text">
         <string>电压标定数据</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_VoltageCalibration">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="2">
       <widget class="QProgressBar" name="progressBar_VoltageCalibration">
        <property name="value">
         <number>0</number>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QPushButton" name="startVoltageCalibration_Btn">
        <property name="text">
         <string>手动标定</string>
        </property>
       </widget>
      </item>
      <item row="0" column="4">
       <widget class="QPushButton" name="abortVoltageCalibration_Btn">
        <property name="text">
         <string>停止标定</string>
        </property>
        <property name="enabled">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="5">
       <widget class="QTableWidget" name="voltageCalibrationTable">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>150</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="4" column="0" colspan="2">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>539</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="4" column="2">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
