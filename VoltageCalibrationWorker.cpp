#include "VoltageCalibrationWorker.h"
#include <QDebug>
#include <QThread>
#include <QSerialPort>
#include <QTcpSocket>
#include <cmath>
#include <cstring>

VoltageCalibrationWorker::VoltageCalibrationWorker(QObject *parent)
    : QObject(parent), m_command<PERSON>and<PERSON>(nullptr), m_abortRequested(false), m_isRunning(false)
{
}

VoltageCalibrationWorker::~VoltageCalibrationWorker()
{
}

void VoltageCalibrationWorker::setDeviceConfig(const CalDeviceConfig &config)
{
    m_deviceConfig = config;
}

void VoltageCalibrationWorker::setCommandHandler(CommandHandlerFunc handler)
{
    m_commandHandler = handler;
}

void VoltageCalibrationWorker::startVoltageCalibration()
{
    if (m_isRunning)
    {
        return;
    }

    m_isRunning = true;
    m_abortRequested = false;

    emit logMessage("开始电压标定...");
    emit calibrationProgress(0);

    // 使用定时器延迟执行，避免阻塞UI
    QTimer::singleShot(100, this, &VoltageCalibrationWorker::performCalibration);
}

void VoltageCalibrationWorker::abortCalibration()
{
    if (!m_isRunning)
    {
        return;
    }

    m_abortRequested = true;
    emit logMessage("电压标定被用户中止");
}

void VoltageCalibrationWorker::performCalibration()
{
    bool success = false;

    try
    {
        emit logMessage("开始标定板卡1的通道1...");

        // 标定0mV档位
        emit logMessage("=== 开始0mV档位标定 ===");
        emit calibrationProgress(10);

        if (m_abortRequested)
        {
            throw std::runtime_error("标定被中止");
        }

        bool level0Success = calibrateVoltageLevel(VOLTAGE_LEVEL_0MV, "0mV");
        if (!level0Success)
        {
            throw std::runtime_error("0mV档位标定失败");
        }

        emit calibrationProgress(50);

        // 标定50mV档位
        emit logMessage("=== 开始50mV档位标定 ===");

        if (m_abortRequested)
        {
            throw std::runtime_error("标定被中止");
        }

        bool level50Success = calibrateVoltageLevel(VOLTAGE_LEVEL_50MV, "50mV");
        if (!level50Success)
        {
            throw std::runtime_error("50mV档位标定失败");
        }

        emit calibrationProgress(100);
        emit logMessage("电压标定完成！");
        success = true;
    }
    catch (const std::exception &e)
    {
        emit logMessage(QString("电压标定失败: %1").arg(e.what()));
        success = false;
    }

    m_isRunning = false;
    emit calibrationFinished(success);
}

bool VoltageCalibrationWorker::calibrateVoltageLevel(double targetVoltage, const QString &levelName)
{
    // 提示用户设置754输出电压
    QString promptMessage = QString("请将754设置输出%1，完成后点击确定按钮").arg(levelName);
    if (!showUserPrompt(promptMessage))
    {
        emit logMessage("用户取消了电压标定");
        return false;
    }

    // 执行该档位的标定
    return performVoltageLevelCalibration(targetVoltage);
}

bool VoltageCalibrationWorker::performVoltageLevelCalibration(double targetVoltage, int maxAttempts)
{
    for (int attempt = 1; attempt <= maxAttempts; ++attempt)
    {
        if (m_abortRequested)
        {
            return false;
        }

        emit logMessage(QString("第%1轮标定，目标电压: %2mV").arg(attempt).arg(targetVoltage));

        // 写入参考电压值到设备
        QByteArray writeCommand = createVoltageWriteFrame(targetVoltage);
        auto writeResult = sendCommand(writeCommand, "VoltageCalibration_WriteReference");

        if (!writeResult.first)
        {
            emit logMessage(QString("写入参考电压失败: %1").arg(writeResult.second));
            continue;
        }

        emit logMessage("参考电压写入成功，等待设备稳定...");

        // 等待设备稳定
        if (!interruptibleMSleep(STABILIZE_DELAY))
        {
            return false;
        }

        // 读取10次电压值
        QVector<double> voltages;
        QVector<double> deviations;

        for (int round = 0; round < READ_COUNT; ++round)
        {
            if (m_abortRequested)
            {
                return false;
            }

            double voltage = readVoltage();
            if (voltage == -999.0)
            { // 读取失败的标志值
                emit logMessage(QString("第%1次读取电压失败").arg(round + 1));
                break;
            }

            voltages.append(voltage);
            double deviation = std::abs(voltage - targetVoltage);
            deviations.append(deviation);

            bool roundPassed = (deviation < VOLTAGE_THRESHOLD);
            emit voltageDataUpdate(voltage, deviation, roundPassed);

            emit logMessage(QString("第%1次读取: 电压=%2mV, 偏差=%3mV, %4")
                                .arg(round + 1)
                                .arg(voltage, 0, 'f', 6)
                                .arg(deviation, 0, 'f', 6)
                                .arg(roundPassed ? "合格" : "不合格"));

            // 读取间隔
            if (round < READ_COUNT - 1)
            {
                if (!interruptibleMSleep(READ_INTERVAL))
                {
                    return false;
                }
            }
        }

        // 检查是否通过标定
        if (voltages.size() == READ_COUNT && isCalibrationPassed(deviations))
        {
            emit logMessage(QString("目标电压%1mV标定成功！").arg(targetVoltage));
            return true;
        }

        emit logMessage(QString("第%1轮标定未通过，准备重试...").arg(attempt));
    }

    emit logMessage(QString("目标电压%1mV经过%2轮标定仍未通过").arg(targetVoltage).arg(maxAttempts));
    return false;
}

double VoltageCalibrationWorker::readVoltage()
{
    QByteArray readCommand = createVoltageReadFrame();
    auto result = sendCommand(readCommand, "VoltageCalibration_ReadVoltage");

    if (result.first)
    {
        return result.second.toDouble();
    }
    else
    {
        emit logMessage(QString("读取电压失败: %1").arg(result.second));
        return -999.0; // 错误标志值
    }
}

bool VoltageCalibrationWorker::isCalibrationPassed(const QVector<double> &deviations, double threshold)
{
    if (deviations.size() < 3)
    {
        return false;
    }

    // 检查最后三次测量的偏差是否都在阈值内
    for (int i = deviations.size() - 3; i < deviations.size(); ++i)
    {
        if (deviations[i] >= threshold)
        {
            return false;
        }
    }

    return true;
}

QByteArray VoltageCalibrationWorker::createVoltageWriteFrame(double voltage)
{
    // 创建写入电压值的Modbus命令
    // 使用单精度浮点数格式写入到地址0x0441

    // 计算寄存器地址（通道0，单精度占用2个寄存器）
    uint16_t addr = VOLTAGE_WRITE_ADDR;
    uint8_t addr_high = (addr >> 8) & 0xFF;
    uint8_t addr_low = addr & 0xFF;

    // 准备单精度浮点数据（小端序）
    QByteArray byteArray;
    float float_value = static_cast<float>(voltage);
    byteArray.resize(4);
    memcpy(byteArray.data(), &float_value, 4);

    // 构造 Modbus-RTU 帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(0x01));      // 设备地址
    frame.append(static_cast<char>(0x10));      // 功能码 10（写多个寄存器）
    frame.append(static_cast<char>(addr_high)); // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));  // 寄存器地址低字节
    frame.append(static_cast<char>(0x00));      // 寄存器数量高字节
    frame.append(static_cast<char>(0x02));      // 寄存器数量低字节（2个寄存器）
    frame.append(static_cast<char>(0x04));      // 字节数（4字节）
    frame.append(byteArray);                    // 数据（4字节）

    return frame;
}

QByteArray VoltageCalibrationWorker::createVoltageReadFrame()
{
    // 创建读取CH1电压值的Modbus命令
    // 从地址0x003D读取单精度浮点数（2个寄存器）

    uint16_t addr = VOLTAGE_READ_ADDR;
    uint8_t addr_high = (addr >> 8) & 0xFF;
    uint8_t addr_low = addr & 0xFF;

    // 构造 Modbus RTU 请求帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(0x01));      // 设备地址
    frame.append(static_cast<char>(0x03));      // 功能码 03（读取保持寄存器）
    frame.append(static_cast<char>(addr_high)); // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));  // 寄存器地址低字节
    frame.append(static_cast<char>(0x00));      // 寄存器数量高字节
    frame.append(static_cast<char>(0x02));      // 寄存器数量低字节（2个寄存器）

    return frame;
}

QPair<bool, QString> VoltageCalibrationWorker::sendCommand(const QByteArray &command, const QString &commandType)
{
    if (m_commandHandler)
    {
        // 为电压标定命令添加Modbus命令包装
        QByteArray modbusCommand = createModbusCommand(QString::fromLatin1(command.toHex()));
        return m_commandHandler(modbusCommand, commandType);
    }

    return {false, "命令处理函数未设置"};
}

QByteArray VoltageCalibrationWorker::createModbusCommand(const QString &hexCommand)
{
    // 将十六进制字符串转换为字节数组
    QByteArray data = QByteArray::fromHex(hexCommand.toLatin1());

    // 计算CRC16
    uint16_t crc = calculateCRC16(data);

    // 添加CRC到数据末尾（小端序）
    data.append(static_cast<char>(crc & 0xFF));        // CRC低字节
    data.append(static_cast<char>((crc >> 8) & 0xFF)); // CRC高字节

    return data;
}

uint16_t VoltageCalibrationWorker::calculateCRC16(const QByteArray &data)
{
    uint16_t crc = 0xFFFF;

    for (int i = 0; i < data.size(); ++i)
    {
        crc ^= static_cast<uint8_t>(data[i]);

        for (int j = 0; j < 8; ++j)
        {
            if (crc & 0x0001)
            {
                crc = (crc >> 1) ^ 0xA001;
            }
            else
            {
                crc >>= 1;
            }
        }
    }

    return crc;
}

bool VoltageCalibrationWorker::interruptibleSleep(int seconds)
{
    return interruptibleMSleep(seconds * 1000);
}

bool VoltageCalibrationWorker::interruptibleMSleep(int milliseconds)
{
    const int checkInterval = 100; // 每100ms检查一次中止请求
    int elapsed = 0;

    while (elapsed < milliseconds)
    {
        if (m_abortRequested)
        {
            return false;
        }

        int sleepTime = qMin(checkInterval, milliseconds - elapsed);
        QThread::msleep(sleepTime);
        elapsed += sleepTime;
    }

    return true;
}

bool VoltageCalibrationWorker::showUserPrompt(const QString &message)
{
    // 在主线程中显示消息框
    bool result = false;
    QMetaObject::invokeMethod(QApplication::instance(), [&]()
                              {
        int ret = QMessageBox::question(nullptr, "电压标定", message,
                                       QMessageBox::Ok | QMessageBox::Cancel);
        result = (ret == QMessageBox::Ok); }, Qt::BlockingQueuedConnection);

    return result;
}
