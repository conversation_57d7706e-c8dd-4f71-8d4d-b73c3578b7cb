﻿#include "mainwindow.h"

#include <QApplication>
#include <QComboBox>
#include <QScreen>

int main(int argc, char *argv[])
{

// Enable high DPI scaling
#if QT_VERSION >= QT_VERSION_CHECK(5, 6, 0)
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QCoreApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
#endif

    QApplication a(argc, argv);

    QString version = "1.3.0";
    a.setApplicationVersion(version);

    // 1.0.1 修复619A-PLUS标定合格指标
    // 1.0.2 修复618A-PLUS写入外部校准值返回数据长度不一致问题
    // 1.0.3 新增设备类型
    // 1.0.4 新增618A NTC-32-TIME 分四量程-第一量程（20k）（复位：1-32）->第四量程（1k）（97-128）
    // 1.0.5 写序列号固定0E功能码
    // 1.0.6 取消标定流程自动保存数据库记录 新增1618A(变更标定阈值判断逻辑、数据库保存序列号型号等字段变更为板卡)
    // 特殊在于calibrationworker中的deviceconfig.name值变为"1618A-NTC-01"格式，与verificationworker中的deviceconfig.name值"1618A"格式不同

    // 1.1.0 新增TM18ND-P 协议用的TM18 且导出报告表头只需阻值偏差 除了代码部分 数据库表字段可能也受影响
    // 1.1.1 修正允许偏差
    // 1.1.2 变更模板校准日期->有效期限
    // 1.1.3 更新界面ui
    // 1.1.4 有效期至
    // 1.1.5 修复部分型号写序列号返回长度不匹配
    // 1.1.6 拉长切换通道开关后的等待时间 新增指定等待时间（默认30s）优化停止校准响应速度
    // 1.1.7 新增 619A NTC-32 PLUS 多通道电阻校准 double双精度
    // 1.1.8 1611系列 - 1611A-HT-PT-100/1000 带透传的 修复校准页面型号切换1618A后切回bug
    // 1.1.9 修改报告导出位数
    // 1.1.10 修正1618A-N19 校准精度1mk->5mk、1mk、1mk  新增板卡滤波次数

    // 1.2.0 修改单阻值分批次校准->单通道所有阻值校准 减少插拔；进度条计算问题;优化默认参考电阻通道勾选问题
    // 1.2.1 修正619 NTC32 1k指标->1mk
    // 1.2.2 修复切换通道开关未延迟等待
    // 1.2.3 修改logo
    // 1.2.4 修正logo 高保真; 优化1611ht读写序列号+退出透传提示;修复错误滤波次数地址，屏蔽波特率选项
    // 1.2.5 新增设备型号618A PLUS（6NTC+2P-AP23）;新增TM18RD-P(导出报告不带等效温度偏差)
    // 1.2.6 修正TM14RD显示挡位问题(读阻值Nan--需复位)
    // 1.2.7 新增TM24ND-P-S(若智协议格式不同，单个寄存器的操作如波特率、滤波次数、复位等写入的值为大端；多个寄存器如读阻值仍然按小端解析；)
    // 1.2.8 1618A->1618A-N  新增1618A-L带透传（使用汇总版协议、不使用1618A-L协议 ）；修正1611A-HT-PT1000 三个参考阻值
    // 1.2.9 新增1618A-RTD，1k-参考1 写入地址0441 ，100-参考2 0461; 读取板卡精度0X0201 02代表RTD 01代表R19(02 R18) 2025-07-22

    // 1.3.0 修正1618A-L协议地址使用单独协议而非汇总版 2025-07-23


    // Tasks：新增1618A-TC; 实现按钮防抖机制

    // 当前的自动标定 当某通道失败时 仍然会标定剩余通道 导致之前的自动保存（只会存所有通道通过的记录）取消 有鸡会优化
    // 可优化保存项目数据后显示项目名到模态框 同时将滚动条拉到底部

    qRegisterMetaType<CalDeviceConfig>("CalDeviceConfig");
    qRegisterMetaType<QVector<QPair<double, double>>>("QVector<QPair<double, double>>");

    qRegisterMetaType<AdjDeviceConfig>("AdjDeviceConfig");
    qRegisterMetaType<ProjectData>("ProjectData");
    qRegisterMetaType<ChannelData>("ChannelData");
    qRegisterMetaType<ReferenceData>("ReferenceData");
    qRegisterMetaType<QVector<double>>("QVector<double>");

    // 获取主屏幕以计算缩放比例
    QScreen *screen = QGuiApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();

    // 计算缩放因子 (基于1920x1080作为参考分辨率)
    double widthScale = screenGeometry.width() / 1920.0;
    double heightScale = screenGeometry.height() / 1080.0;
    double scaleFactor = qMin(widthScale, heightScale);

    // 设置默认字体大小并进行缩放
    int baseFontSize = 13;
    int scaledFontSize = qRound(baseFontSize * scaleFactor);

    // 确保字体大小在合理范围内
    scaledFontSize = qMax(11, qMin(scaledFontSize, 18));

    QFont defaultFont = a.font();
    defaultFont.setPointSize(scaledFontSize);
    a.setFont(defaultFont);

    // 找到所有 QComboBox 并设置属性
    QList<QComboBox *> comboBoxes = a.findChildren<QComboBox *>();
    for (QComboBox *box : comboBoxes)
    {
        box->setMaxVisibleItems(6); // 设置最大显示项数
        box->setSizeAdjustPolicy(QComboBox::AdjustToMinimumContentsLengthWithIcon);
    }

    // 设置全局样式表
    QString styleSheet = R"(
        /* 全局样式 */

        QWidget {
            font-family: 'Segoe UI', 'Arial', 'Microsoft YaHei';
            font-size: 13px;
        }

        QMainWindow, QDialog {
            background-color: #fafafa;
        }

        /* 滚动条样式 */
        QScrollBar:vertical {
            border: none;
            background: #f0f0f0;
            width: 10px;
            margin: 0px 0px 0px 0px;
        }

        QScrollBar::handle:vertical {
            background: #c0c0c0;
            border-radius: 5px;
            min-height: 30px;
        }

        QScrollBar::handle:vertical:hover {
            background: #a0a0a0;
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }

        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
            background: none;
        }

        /* 输入框样式 */
        QLineEdit {
            padding: 3px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: white;
            selection-background-color: #0078d4;
        }

        QLineEdit:focus {
            border: 1px solid #0078d4;
        }

        /* GroupBox 样式 */
        QGroupBox {
            border: 1px solid #d0d0d0;
            border-radius: 5px;
            margin-top: 3ex; /* 增加 margin-top */
            background-color: #ffffff;
        }

        QGroupBox::title {
            subcontrol-origin: margin;  /*原点基于边框 上边框初始与box边框对齐*/
            subcontrol-position: top left;
            background-color: #fafafa;
            padding: 0 3px;
            left: 10px;
        }

    )";

    a.setStyleSheet(styleSheet);

    MainWindow w;
    w.show();
    return a.exec();
}
